# 在线计算器

一个基于服务器/浏览器架构的计算器应用程序，使用Vue.js作为前端，Python FastAPI作为后端。

## 项目结构

```
OnlineCalculator/
├── frontend/          # Vue.js前端应用
│   ├── src/
│   │   ├── components/
│   │   │   └── Calculator.vue    # 计算器组件
│   │   ├── App.vue              # 主应用组件
│   │   └── main.js              # 应用入口
│   ├── package.json
│   └── vite.config.js
├── backend/           # Python FastAPI后端
│   ├── main.py        # FastAPI应用主文件
│   ├── calculator.py  # 计算逻辑模块
│   └── requirements.txt
└── README.md
```

## 功能特性

### 前端功能
- 数字按钮 (0-9)
- 运算符按钮 (+, -, ×, ÷)
- 等号按钮 (=)
- 清除按钮 (C)
- 退格按钮 (⌫)
- 小数点按钮 (.)
- 实时显示当前输入和计算结果
- 错误提示
- 键盘支持
- 响应式设计

### 后端功能
- 安全的数学表达式计算
- REST API接口
- 错误处理和验证
- CORS支持
- 日志记录

### API接口
- `GET /` - API信息
- `GET /health` - 健康检查
- `POST /calculate` - 计算数学表达式

## 安装和运行

### 前提条件
- Node.js (版本 16 或更高)
- Python (版本 3.8 或更高)
- npm 或 yarn

### 后端设置

1. 进入后端目录：
```bash
cd backend
```

2. 安装Python依赖：
```bash
pip install -r requirements.txt
```

3. 启动后端服务器：
```bash
python main.py
```

后端服务器将在 `http://localhost:8000` 启动。

### 前端设置

1. 进入前端目录：
```bash
cd frontend
```

2. 安装依赖：
```bash
npm install
```

3. 启动开发服务器：
```bash
npm run dev
```

前端应用将在 `http://localhost:5173` 启动。

## 使用方法

1. 确保后端服务器在 `http://localhost:8000` 运行
2. 确保前端应用在 `http://localhost:5173` 运行
3. 在浏览器中打开前端应用
4. 使用鼠标点击按钮或键盘输入进行计算

### 键盘快捷键
- `0-9` - 输入数字
- `+`, `-`, `*`, `/` - 输入运算符
- `Enter` 或 `=` - 计算结果
- `Escape` 或 `C` - 清除
- `Backspace` - 删除最后一个字符
- `.` - 输入小数点

## 技术栈

### 前端
- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 现代化的前端构建工具
- **CSS3** - 样式和响应式设计

### 后端
- **FastAPI** - 现代、快速的Python Web框架
- **Uvicorn** - ASGI服务器
- **Pydantic** - 数据验证和设置管理

## 安全特性

- 使用AST（抽象语法树）安全地解析和计算数学表达式
- 防止代码注入攻击
- 输入验证和清理
- 错误处理和用户友好的错误消息

## 开发

### 后端开发
- 修改 `backend/main.py` 来添加新的API端点
- 修改 `backend/calculator.py` 来扩展计算功能
- 使用 `uvicorn main:app --reload` 进行热重载开发

### 前端开发
- 修改 `frontend/src/components/Calculator.vue` 来更新UI
- 修改 `frontend/src/App.vue` 来更新应用布局
- Vite提供热重载功能，保存文件后自动更新

## 故障排除

### 常见问题

1. **前端无法连接到后端**
   - 确保后端服务器在 `http://localhost:8000` 运行
   - 检查CORS设置
   - 确保防火墙没有阻止连接

2. **计算错误**
   - 检查输入的数学表达式是否有效
   - 查看浏览器控制台的错误信息
   - 查看后端日志

3. **依赖安装问题**
   - 确保Node.js和Python版本符合要求
   - 尝试清除缓存：`npm cache clean --force`
   - 尝试删除 `node_modules` 并重新安装

## 许可证

MIT License
