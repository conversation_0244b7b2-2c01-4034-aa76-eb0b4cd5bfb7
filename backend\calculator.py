"""
计算器逻辑模块
提供安全的数学表达式计算功能
"""
import re
import ast
import operator


class Calculator:
    """安全的计算器类"""
    
    # 支持的运算符
    OPERATORS = {
        ast.Add: operator.add,
        ast.Sub: operator.sub,
        ast.Mult: operator.mul,
        ast.Div: operator.truediv,
        ast.USub: operator.neg,
        ast.UAdd: operator.pos,
    }
    
    def __init__(self):
        pass
    
    def evaluate_expression(self, expression: str) -> float:
        """
        安全地计算数学表达式
        
        Args:
            expression (str): 要计算的数学表达式
            
        Returns:
            float: 计算结果
            
        Raises:
            ValueError: 当表达式无效时
            ZeroDivisionError: 当除零时
        """
        try:
            # 清理表达式，只保留数字、运算符和小数点
            cleaned_expression = re.sub(r'[^0-9+\-*/().\s]', '', expression)
            
            if not cleaned_expression.strip():
                raise ValueError("表达式为空")
            
            # 解析表达式为AST
            node = ast.parse(cleaned_expression, mode='eval')
            
            # 计算结果
            result = self._evaluate_node(node.body)
            
            return result
            
        except SyntaxError:
            raise ValueError("无效的数学表达式")
        except ZeroDivisionError:
            raise ZeroDivisionError("除零错误")
        except Exception as e:
            raise ValueError(f"计算错误: {str(e)}")
    
    def _evaluate_node(self, node):
        """递归计算AST节点"""
        if isinstance(node, ast.Constant):  # Python 3.8+
            return node.value
        elif isinstance(node, ast.Num):  # Python < 3.8
            return node.n
        elif isinstance(node, ast.BinOp):
            left = self._evaluate_node(node.left)
            right = self._evaluate_node(node.right)
            operator_func = self.OPERATORS.get(type(node.op))
            if operator_func is None:
                raise ValueError(f"不支持的运算符: {type(node.op)}")
            return operator_func(left, right)
        elif isinstance(node, ast.UnaryOp):
            operand = self._evaluate_node(node.operand)
            operator_func = self.OPERATORS.get(type(node.op))
            if operator_func is None:
                raise ValueError(f"不支持的一元运算符: {type(node.op)}")
            return operator_func(operand)
        else:
            raise ValueError(f"不支持的节点类型: {type(node)}")


def format_result(result) -> str:
    """
    格式化计算结果

    Args:
        result: 计算结果 (可能是int或float)

    Returns:
        str: 格式化后的结果字符串
    """
    # 如果是整数类型，直接返回字符串
    if isinstance(result, int):
        return str(result)
    # 如果是浮点数且为整数值，显示为整数
    elif isinstance(result, float) and result.is_integer():
        return str(int(result))
    else:
        # 保留最多10位小数，去除尾随零
        return f"{result:.10f}".rstrip('0').rstrip('.')
