'use strict'

/**
 * @typedef {object} RemovedRule
 * @property {string} ruleName
 * @property {string[]} replacedBy
 * @property {string} deprecatedInVersion
 * @property {string} removedInVersion
 */

/** @type {RemovedRule[]} */
module.exports = [
  {
    ruleName: 'component-tags-order',
    replacedBy: ['block-order'],
    deprecatedInVersion: 'v9.16.0',
    removedInVersion: 'v10.0.0'
  },
  {
    ruleName: 'experimental-script-setup-vars',
    replacedBy: [],
    deprecatedInVersion: 'v7.13.0',
    removedInVersion: 'v9.0.0'
  },
  {
    ruleName: 'name-property-casing',
    replacedBy: ['component-definition-name-casing'],
    deprecatedInVersion: 'v7.0.0',
    removedInVersion: 'v9.0.0'
  },
  {
    ruleName: 'no-confusing-v-for-v-if',
    replacedBy: ['no-use-v-if-with-v-for'],
    deprecatedInVersion: 'v5.0.0',
    removedInVersion: 'v9.0.0'
  },
  {
    ruleName: 'no-invalid-model-keys',
    replacedBy: ['valid-model-definition'],
    deprecatedInVersion: 'v9.0.0',
    removedInVersion: 'v10.0.0'
  },
  {
    ruleName: 'no-ref-object-destructure',
    replacedBy: ['no-ref-object-reactivity-loss'],
    deprecatedInVersion: 'v9.17.0',
    removedInVersion: 'v10.0.0'
  },
  {
    ruleName: 'no-setup-props-destructure',
    replacedBy: ['no-setup-props-reactivity-loss'],
    deprecatedInVersion: 'v9.17.0',
    removedInVersion: 'v10.0.0'
  },
  {
    ruleName: 'no-unregistered-components',
    replacedBy: ['no-undef-components'],
    deprecatedInVersion: 'v8.4.0',
    removedInVersion: 'v9.0.0'
  },
  {
    ruleName: 'script-setup-uses-vars',
    replacedBy: [],
    deprecatedInVersion: 'v9.0.0',
    removedInVersion: 'v10.0.0'
  },
  {
    ruleName: 'v-on-function-call',
    replacedBy: ['v-on-handler-style'],
    deprecatedInVersion: 'v9.7.0',
    removedInVersion: 'v10.0.0'
  }
]
