<script setup>
import Calculator from './components/Calculator.vue'
</script>

<template>
  <div id="app">
    <header>
      <h1>在线计算器</h1>
    </header>
    <main>
      <Calculator />
    </main>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

#app {
  text-align: center;
  color: white;
  width: 100%;
  max-width: 500px;
  padding: 20px;
}

header h1 {
  margin-bottom: 2rem;
  font-size: 2.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

main {
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (max-width: 480px) {
  header h1 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  #app {
    padding: 10px;
  }
}
</style>
