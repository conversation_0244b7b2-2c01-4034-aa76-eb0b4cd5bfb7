<template>
  <div class="calculator">
    <div class="display">
      <div class="expression">{{ currentExpression || '0' }}</div>
      <div class="result" v-if="result">= {{ result }}</div>
      <div class="error" v-if="error">错误: {{ error }}</div>
    </div>
    
    <div class="buttons">
      <button @click="clear" class="btn btn-clear">C</button>
      <button @click="deleteLast" class="btn btn-operator">⌫</button>
      <button @click="appendOperator('/')" class="btn btn-operator">÷</button>
      <button @click="appendOperator('*')" class="btn btn-operator">×</button>
      
      <button @click="appendNumber('7')" class="btn btn-number">7</button>
      <button @click="appendNumber('8')" class="btn btn-number">8</button>
      <button @click="appendNumber('9')" class="btn btn-number">9</button>
      <button @click="appendOperator('-')" class="btn btn-operator">-</button>
      
      <button @click="appendNumber('4')" class="btn btn-number">4</button>
      <button @click="appendNumber('5')" class="btn btn-number">5</button>
      <button @click="appendNumber('6')" class="btn btn-number">6</button>
      <button @click="appendOperator('+')" class="btn btn-operator">+</button>
      
      <button @click="appendNumber('1')" class="btn btn-number">1</button>
      <button @click="appendNumber('2')" class="btn btn-number">2</button>
      <button @click="appendNumber('3')" class="btn btn-number">3</button>
      <button @click="calculate" class="btn btn-equals" rowspan="2">=</button>
      
      <button @click="appendNumber('0')" class="btn btn-number btn-zero">0</button>
      <button @click="appendNumber('.')" class="btn btn-number">.</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const currentExpression = ref('')
const result = ref('')
const error = ref('')
const isCalculating = ref(false)

// API基础URL
const API_BASE_URL = 'http://localhost:8000'

// 添加数字
const appendNumber = (number) => {
  error.value = ''
  result.value = ''
  currentExpression.value += number
}

// 添加运算符
const appendOperator = (operator) => {
  error.value = ''
  result.value = ''
  
  // 转换显示符号为计算符号
  const actualOperator = operator === '×' ? '*' : operator === '÷' ? '/' : operator
  
  // 防止连续添加运算符
  const lastChar = currentExpression.value.slice(-1)
  if (lastChar && '+-*/'.includes(lastChar)) {
    currentExpression.value = currentExpression.value.slice(0, -1) + actualOperator
  } else if (currentExpression.value) {
    currentExpression.value += actualOperator
  }
}

// 清除
const clear = () => {
  currentExpression.value = ''
  result.value = ''
  error.value = ''
}

// 删除最后一个字符
const deleteLast = () => {
  error.value = ''
  result.value = ''
  currentExpression.value = currentExpression.value.slice(0, -1)
}

// 计算表达式
const calculate = async () => {
  if (!currentExpression.value.trim()) {
    return
  }
  
  isCalculating.value = true
  error.value = ''
  
  try {
    const response = await fetch(`${API_BASE_URL}/calculate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        expression: currentExpression.value
      })
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    if (data.success) {
      result.value = data.result
    } else {
      error.value = data.error || '计算错误'
    }
  } catch (err) {
    console.error('计算请求失败:', err)
    error.value = '无法连接到服务器'
  } finally {
    isCalculating.value = false
  }
}

// 键盘事件处理
const handleKeyPress = (event) => {
  const key = event.key
  
  if (key >= '0' && key <= '9' || key === '.') {
    appendNumber(key)
  } else if (key === '+' || key === '-') {
    appendOperator(key)
  } else if (key === '*') {
    appendOperator('*')
  } else if (key === '/') {
    event.preventDefault() // 防止浏览器搜索
    appendOperator('/')
  } else if (key === 'Enter' || key === '=') {
    calculate()
  } else if (key === 'Escape' || key === 'c' || key === 'C') {
    clear()
  } else if (key === 'Backspace') {
    deleteLast()
  }
}

// 组件挂载时添加键盘监听
import { onMounted, onUnmounted } from 'vue'

onMounted(() => {
  window.addEventListener('keydown', handleKeyPress)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyPress)
})
</script>

<style scoped>
.calculator {
  background: white;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 350px;
  margin: 0 auto;
}

.display {
  background: #1a1a1a;
  color: white;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  min-height: 80px;
  text-align: right;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.expression {
  font-size: 1.5rem;
  margin-bottom: 5px;
  word-break: break-all;
}

.result {
  font-size: 1.2rem;
  color: #4CAF50;
  opacity: 0.8;
}

.error {
  font-size: 1rem;
  color: #f44336;
}

.buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
}

.btn {
  border: none;
  border-radius: 10px;
  font-size: 1.2rem;
  font-weight: bold;
  height: 60px;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn:active {
  transform: translateY(0);
}

.btn-number {
  background: #f5f5f5;
  color: #333;
}

.btn-number:hover {
  background: #e0e0e0;
}

.btn-operator {
  background: #ff9500;
  color: white;
}

.btn-operator:hover {
  background: #e6850e;
}

.btn-clear {
  background: #ff3b30;
  color: white;
}

.btn-clear:hover {
  background: #e6342a;
}

.btn-equals {
  background: #ff9500;
  color: white;
  grid-row: span 2;
}

.btn-equals:hover {
  background: #e6850e;
}

.btn-zero {
  grid-column: span 2;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .calculator {
    max-width: 300px;
    padding: 15px;
  }
  
  .btn {
    height: 50px;
    font-size: 1rem;
  }
  
  .expression {
    font-size: 1.2rem;
  }
}
</style>
